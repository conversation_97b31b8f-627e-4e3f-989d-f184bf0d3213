tbody td {
  font-family: "Roboto", "Helvetica Neue", sans-serif;
  font-size: 14px;
  color: #82888e;
  line-height: 1.5;
  letter-spacing: normal;
  font-style: normal;
  font-weight: normal;
}

.table-head {
  background-color: #4285f4;
}

.custom-row-lines th,
.custom-row-lines td {
  border-top: none;
  border-right: none;
  border-left: none;
  border-bottom: 1px solid #dee2e6;
}

.custom-row-lines thead th {
  border-bottom: 2px solid #4285f4;
}

.custom-row-lines tr:last-child td {
  border-radius: 4% 4% 0 0; /* Rounded corners for the last row */
}

a {
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

a:hover {
  background-color: #d0d7df; /* Darker shade for hover */
  color: #ffffff; /* Ensure text is visible */
}

.filter-btn {
  border-radius: 8px;
}

:host {
  // Table container with fixed height and scrolling
  .table-container {
    max-height: calc(100vh - 300px); // Adjust based on your layout
    min-height: 400px; // Minimum height to ensure visibility
    display: flex;
    flex-direction: column;
    margin-bottom: 1rem;
  }

  .table-responsive {
    flex: 1;
    overflow: auto;

    // Ensure scrollbar is always visible on Windows
    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  }

  // Sticky header
  .sticky-top {
    position: sticky;
    top: 0;
    z-index: 1;
  }

  .table {
    td {
      padding: 1rem 0.75rem; // Increased vertical padding for table cells
      vertical-align: middle;
    }

    // Action buttons styling
    .action-buttons {
      padding: 0.5rem 0.25rem;

      .d-flex {
        gap: 0.25rem !important;
      }

      .btn {
        margin: 0;
        padding: 0.25rem !important;
        min-width: 28px;
        height: 28px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: rgba(0, 0, 0, 0.05);
        }

        i {
          font-size: 1rem;
        }
      }
    }
  }
}

// Add the same styling as in the resource list component
:host {
  // Consistent form control styling
  .p-inputtext,
  .p-dropdown {
    width: 100%;
    height: 38px;
  }

  // Fix for title field alignment and icon placement
  .p-input-icon-left {
    display: block;
    position: relative;
    width: 100%;

    i {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: 0.75rem;
      z-index: 1;
      color: #6c757d;
    }

    input {
      width: 100%;
      padding-left: 2.5rem;
    }
  }

  // Ensure dropdown and input have same height
  .p-dropdown {
    display: flex;
    align-items: center;

    .p-dropdown-label {
      padding-top: 0.4rem;
      padding-bottom: 0.4rem;
    }
  }

  // Consistent spacing
  .card-body.py-2 {
    padding: 1rem;
  }

  .form-label.small {
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #495057;
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    .row.g-3 > div {
      margin-bottom: 0.5rem;
    }

    // Make buttons more compact on mobile
    .btn {
      padding: 0.375rem 0.5rem;
      font-size: 0.875rem;
    }
  }

  // Header styling
  h2 {
    margin-bottom: 0;
    white-space: nowrap;
  }

  // Button styling
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 38px;
  }

  // Add some space between icon and text
  .btn i + span {
    margin-left: 0.25rem;
  }

  // Pagination container styling
  .pagination-container {
    padding: 0.5rem 0;
    background-color: #fff;
    position: relative;
    z-index: 2;
    // Using global pagination styles from styles.scss
  }
}

// Using global PrimeNG paginator styles from styles.scss

// Sortable header styling
.sortable-header {
  user-select: none;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  i {
    margin-left: 0.5rem;
    font-size: 0.875rem;
    transition: transform 0.2s ease;
  }
}
