import {
  Component,
  OnInit,
  inject,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
} from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { finalize } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import {
  Event,
  EventStatus,
  EventType,
  EventLocationType,
  Category,
} from '../../../Core/Models/events';
import { AuthService } from '../../../Core/Services/auth.service';
import { EventsService } from '../../../Core/Services/events.service';
import { UserDetailsService } from '../../../Core/Services/UserDetails.service';
import { DateUtilsService } from '../../../Core/Services/date-utils.service';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'app-event-list',
  standalone: false,
  templateUrl: './event-list.component.html',
  styleUrl: './event-list.component.scss',
  providers: [MessageService],
  // Temporarily remove OnPush to debug the issue
  // changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EventListComponent implements OnInit {
  Math = Math;
  EventLocationType = EventLocationType; // Make enum available to template
  events: Event[] = [];
  filteredEvents: Event[] = [];
  error: string | null = null;
  isLoading: boolean = false;
  router = inject(Router);
  showFilters: boolean = false;
  filterForm: FormGroup;
  isGlobalAdmin: boolean = false;

  activeTab: 'all' | 'pending' = 'all';
  pendingReviewCount: number = 0;

  // Pagination
  currentPage: number = 1;
  pageSize: number = 5;
  totalItems: number = 0;
  totalPages: number = 0;

  // Sorting
  currentSortField?: string;
  currentSortOrder: 'asc' | 'desc' = 'asc';

  // Filter options
  eventStatusOptions: { name: string; value: string }[] = [
    { name: 'All', value: '' },
    { name: 'Draft', value: 'Draft' },
    { name: 'Submitted', value: 'Submitted' },
    { name: 'Approved', value: 'Approved' },
    { name: 'Rejected', value: 'Rejected' },
    { name: 'Cancelled', value: 'Cancelled' },
  ];

  approvalStatusOptions: { name: string; value: string }[] = [
    { name: 'All', value: '' },
    { name: 'Approved', value: 'true' },
    { name: 'Not Approved', value: 'false' },
  ];

  typeOptions: { name: string; value: string | number }[] = [
    { name: 'All', value: '' },
    { name: 'Appearance Or Signing', value: EventType.AppearanceOrSigning },
    { name: 'Attraction', value: EventType.Attraction },
    { name: 'Camp Trip Or Retreat', value: EventType.CampTripOrRetreat },
    {
      name: 'Class Training Or Workshop',
      value: EventType.ClassTrainingOrWorkshop,
    },
    { name: 'Concert Or Performance', value: EventType.ConcertOrPerformance },
    { name: 'Conference', value: EventType.Conference },
    { name: 'Convention', value: EventType.Convention },
    { name: 'Dinner Or Gala', value: EventType.DinnerOrGala },
    { name: 'Festival Or Fair', value: EventType.FestivalOrFair },
    { name: 'Games Or Competition', value: EventType.GamesOrCompetition },
    {
      name: 'Meeting Or Networking Event',
      value: EventType.MeetingOrNetworkingEvent,
    },
    { name: 'Other', value: EventType.Other },
    {
      name: 'Party Or Social Gathering',
      value: EventType.PartyOrSocialGathering,
    },
    { name: 'Rally', value: EventType.Rally },
    { name: 'Screening', value: EventType.Screening },
    { name: 'Seminar Or Talk', value: EventType.SeminarOrTalk },
    { name: 'Tour', value: EventType.Tour },
    { name: 'Tournament', value: EventType.Tournament },
    {
      name: 'Trade Show Consumer Show Or Expo',
      value: EventType.TradeShowConsumerShowOrExpo,
    },
  ];

  categoryOptions: { name: string; value: string | number }[] = [
    { name: 'All', value: '' },
    { name: 'Careers And Employment', value: Category.CareersAndEmployment },
    { name: 'Community Resources', value: Category.CommunityResources },
    { name: 'Early Childhood', value: Category.EarlyChildhood },
    { name: 'Health And Wellness', value: Category.HealthWellness },
    { name: 'Maternal Health Care', value: Category.MaternalHealthCare },
    { name: 'Rental Housing', value: Category.RentalHousing },
  ];

  organizerOptions: { name: string; value: string }[] = [
    { name: 'All', value: '' },
  ];

  constructor(
    private readonly eventsService: EventsService,
    private readonly userService: UserDetailsService,
    private readonly messageService: MessageService,
    private fb: FormBuilder,
    public authService: AuthService,
    private dateUtils: DateUtilsService,
    private cdr: ChangeDetectorRef,
  ) {
    this.filterForm = this.fb.group({
      searchTerm: [''],
      eventStartDate: [null],
      eventStatus: [''],
      organizer: [''],
      approvalStatus: [''],
      type: [''],
      category: [''],
      submittedOn: [null],
      eventReviewedOn: [null],
    });

    // Debounce filter changes
    this.filterForm.valueChanges.pipe(debounceTime(300)).subscribe(() => {
      this.currentPage = 1;
      this.loadEvents();
    });
  }

  ngOnInit() {
    this.loadEvents();
    // Don't load organizers here - we'll load them only when needed
    this.checkUserRole();
  }

  checkUserRole(): void {
    const userInfo = this.authService.getUserInfo();

    if (userInfo && userInfo.role) {
      // Check if user has Global Admin role
      this.isGlobalAdmin = Array.isArray(userInfo.role)
        ? userInfo.role.includes('Global Admin')
        : userInfo.role === 'Global Admin';
    }
  }

  loadOrganizers(): void {
    // Get unique organizer and submitter names from the current events
    const uniqueNames = new Set<string>();

    this.events.forEach((event) => {
      if (event.organizerName) {
        uniqueNames.add(event.organizerName);
      }
      if (event.submitterName && event.submitterName !== event.organizerName) {
        uniqueNames.add(event.submitterName);
      }
    });

    // Convert to dropdown options and sort alphabetically
    const nameOptions = Array.from(uniqueNames)
      .sort()
      .map((name) => ({
        name: name,
        value: name,
      }));

    this.organizerOptions = [{ name: 'All', value: '' }, ...nameOptions];

    // If no events are loaded yet, fallback to loading users by role
    if (this.events.length === 0) {
      this.userService.getUsersByRole('Event Organizer').subscribe({
        next: (users) => {
          const userOptions = users.map((user) => ({
            name: user.fullName,
            value: user.fullName, // Use name instead of ID for consistent filtering
          }));

          this.organizerOptions = [{ name: 'All', value: '' }, ...userOptions];
        },
        error: (err) => {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load organizers. ' + err.message,
          });
        },
      });
    }
  }

  loadEvents(): void {
    this.error = null;
    this.isLoading = true;

    const params = {
      pageNumber: this.currentPage,
      pageSize: this.pageSize,
      sortField: this.currentSortField,
      sortOrder: this.currentSortOrder,
      filters: this.filterForm.value,
    };

    this.eventsService
      .getEvents(params)
      .pipe(
        finalize(() => {
          this.isLoading = false;
        }),
      )
      .subscribe({
        next: (response) => {
          try {
            if (response) {
              this.events = response.items || [];
              this.totalItems = response.totalItems || 0;
              this.totalPages = response.totalPages || 0;

              this.processEvents();

              // Update organizer options after events are loaded
              if (this.showFilters) {
                this.loadOrganizers();
              }

              // Trigger change detection for OnPush strategy
              this.cdr.markForCheck();
            } else {
              throw new Error('Invalid response format');
            }
          } catch (error) {
            this.error = 'Failed to process event data';
            this.events = [];
            this.totalItems = 0;
            this.totalPages = 0;
            this.cdr.markForCheck();
          }
        },
        error: (err) => {
          this.error = err.message;
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: err.message || 'Failed to load events. Please try again.',
          });
          this.cdr.markForCheck();
        },
      });
  }

  processEvents(): void {
    console.log('🔍 Processing events...', {
      totalEvents: this.events.length,
      activeTab: this.activeTab,
    });

    // Filter out events that are within 30 minutes of starting
    const eventsNotWithin30Min = this.events.filter((event) => {
      if (!event.eventStarts) return true;

      // Convert UTC event start time to IST before comparing
      const eventStartTime = this.dateUtils
        .convertUtcToIst(event.eventStarts)
        .getTime();
      const thirtyMinutesBefore = eventStartTime - 30 * 60 * 1000; // 30 minutes in milliseconds
      const now = new Date().getTime();

      return now < thirtyMinutesBefore;
    });

    // Count pending review events (excluding those within 30 minutes of starting)
    const submittedEvents = eventsNotWithin30Min.filter(
      (event) => event.statusName === 'Submitted',
    );

    this.pendingReviewCount = submittedEvents.length;

    console.log('📊 Event processing results:', {
      eventsNotWithin30Min: eventsNotWithin30Min.length,
      submittedEvents: submittedEvents.length,
      pendingReviewCount: this.pendingReviewCount,
      allEventStatuses: this.events.map((e) => ({
        id: e.id,
        title: e.title,
        status: e.statusName,
      })),
    });

    // Filter events based on active tab
    if (this.activeTab === 'pending') {
      // For pending tab, only show events that are in 'Submitted' status AND not within 30 minutes of starting
      this.filteredEvents = eventsNotWithin30Min.filter(
        (event) => event.statusName === 'Submitted',
      );
    } else {
      // For all tab, show all events
      this.filteredEvents = [...this.events];
    }

    console.log('✅ Filtered events for display:', this.filteredEvents.length);

    // Trigger change detection for OnPush strategy
    this.cdr.markForCheck();
  }

  getFullImagePath(relativePath: string): string {
    if (!relativePath) {
      return 'assets/images/placeholder.jpg';
    }

    // If it's already a full URL (from S3), return it as is
    if (relativePath.startsWith('http')) {
      return relativePath;
    }

    // Check if the path already contains /api/Files
    if (relativePath.includes('/api/Files/')) {
      return `${environment.apiUrl}${relativePath}`;
    }

    // Otherwise, use the Files controller to get the full path
    return `${environment.apiUrl}/api/Files/${relativePath.replace(/^\/+/, '')}`;
  }

  setActiveTab(tab: 'all' | 'pending'): void {
    this.activeTab = tab;
    this.processEvents();
    // Additional change detection trigger for tab switching
    this.cdr.markForCheck();
  }

  toggleFilters(): void {
    this.showFilters = !this.showFilters;

    // Load organizers only when the filter is opened and we haven't loaded them yet
    if (this.showFilters && this.organizerOptions.length <= 1) {
      this.loadOrganizers();
    }
  }

  clearFilters(): void {
    this.filterForm.reset();
    this.currentPage = 1;
    this.loadEvents();
  }

  onDateSelect(date: Date, controlName: string): void {
    // Ensure the date is properly set in the form control
    // This creates a new Date object at midnight UTC to avoid timezone issues
    const utcDate = new Date(
      Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0),
    );

    this.filterForm.get(controlName)?.setValue(utcDate);
  }

  sortEvents(field: keyof Event): void {
    if (this.currentSortField === field) {
      this.currentSortOrder = this.currentSortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      this.currentSortField = field;
      this.currentSortOrder = 'asc';
    }
    this.loadEvents();
  }

  viewEventDetails(eventId: number): void {
    this.router.navigate(['/events/event-details', eventId]);
  }

  addEvent(): void {
    this.router.navigate(['/events/add-event']);
  }

  editEvent(eventId: number): void {
    this.router.navigate(['/events/edit-event', eventId]);
  }

  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.loadEvents();
    }
  }

  get pages(): number[] {
    if (this.totalPages <= 5) {
      return Array.from({ length: this.totalPages }, (_, i) => i + 1);
    }

    if (this.currentPage <= 3) {
      return [1, 2, 3, 4, 5];
    }

    if (this.currentPage >= this.totalPages - 2) {
      return Array.from({ length: 5 }, (_, i) => this.totalPages - 4 + i);
    }

    return Array.from({ length: 5 }, (_, i) => this.currentPage - 2 + i);
  }

  // Check if the event has already started
  hasEventStarted(event: Event): boolean {
    if (!event || !event.eventStarts) return false;

    // Convert UTC event start time to IST before comparing
    const eventStartTime = this.dateUtils
      .convertUtcToIst(event.eventStarts)
      .getTime();
    const now = new Date().getTime();

    return now >= eventStartTime;
  }
  formatEventType(type: string): string {
    switch (type) {
      case 'AppearanceOrSigning':
        return 'Appearance Or Signing';
      case 'Attraction':
        return 'Attraction';
      case 'CampTripOrRetreat':
        return 'Camp Trip Or Retreat';
      case 'ClassTrainingOrWorkshop':
        return 'Class Training Or Workshop';
      case 'ConcertOrPerformance':
        return 'Concert Or Performance';
      case 'Conference':
        return 'Conference';
      case 'Convention':
        return 'Convention';
      case 'DinnerOrGala':
        return 'Dinner Or Gala';
      case 'FestivalOrFair':
        return 'Festival Or Fair';
      case 'GamesOrCompetition':
        return 'Games Or Competition';
      case 'MeetingOrNetworkingEvent':
        return 'Meeting Or Networking Event';
      case 'Other':
        return 'Other';
      case 'PartyOrSocialGathering':
        return 'Party Or Social Gathering';
      case 'Rally':
        return 'Rally';
      case 'Screening':
        return 'Screening';
      case 'SeminarOrTalk':
        return 'Seminar Or Talk';
      case 'Tour':
        return 'Tour';
      case 'Tournament':
        return 'Tournament';
      case 'TradeShowConsumerShowOrExpo':
        return 'Trade Show Consumer Show Or Expo';
      default:
        return type;
    }
  }

  formatEventCategory(category: string): string {
    switch (category) {
      case 'CareersAndEmployment':
        return 'Careers And Employment';
      case 'CommunityResources':
        return 'Community Resources';
      case 'EarlyChildhood':
        return 'Early Childhood';
      case 'HealthWellness':
        return 'Health And Wellness';
      case 'MaternalHealthCare':
        return 'Maternal Health Care';
      case 'RentalHousing':
        return 'Rental Housing';
      default:
        return category;
    }
  }

  // TrackBy function for better performance with *ngFor
  trackByEventId(index: number, event: Event): number {
    return event.id;
  }
}
