<!-- Event List Container -->
<div class="event-list-container">
  <!-- Header with Title and Add Button -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="mb-0">Events</h2>
    <div class="d-flex">
      <button
        pButton
        class="p-button-outlined p-button-danger me-2"
        (click)="toggleFilters()"
      >
        <i class="pi pi-filter me-1"></i>
        {{ showFilters ? "Hide" : " Filters" }}
      </button>
      <button
        pButton
        class="p-button-danger create-event-btn"
        (click)="addEvent()"
        *ngIf="isGlobalAdmin || authService.hasRole('Event Organizer')"
      >
        <i class="pi pi-plus me-1"></i> Create New Event
      </button>
    </div>
  </div>

  <!-- Tabs for All Events and Pending Review -->
  <div class="event-tabs">
    <ul class="nav nav-tabs">
      <li class="nav-item">
        <a
          class="nav-link"
          [class.active]="activeTab === 'all'"
          href="javascript:void(0)"
          (click)="setActiveTab('all')"
          >All</a
        >
      </li>
      <li class="nav-item">
        <a
          class="nav-link"
          [class.active]="activeTab === 'pending'"
          href="javascript:void(0)"
          (click)="setActiveTab('pending')"
        >
          Pending Review
          <span class="badge" *ngIf="pendingReviewCount > 0">{{
            pendingReviewCount
          }}</span>
        </a>
      </li>
    </ul>
  </div>

  <!-- Filter Section -->
  <div class="compact-filter-section" *ngIf="showFilters">
    <div
      class="filter-header d-flex justify-content-between align-items-center mb-2"
    >
      <h5 class="mb-0">Filter Events</h5>
      <p-button
        label="Clear Filters"
        icon="pi pi-filter-slash"
        styleClass="p-button-sm p-button-danger"
        [style]="{ height: '28px', 'font-size': '0.8rem' }"
        (onClick)="clearFilters()"
      ></p-button>
    </div>

    <!-- Filter Form -->
    <form [formGroup]="filterForm">
      <!-- First Row -->
      <div class="filter-header-row">
        <div class="filter-header-item">
          <label for="searchTerm">Search Event</label>
          <div class="p-input-icon-left w-100">
            <i class="pi pi-search"></i>
            <input
              id="searchTerm"
              type="text"
              pInputText
              formControlName="searchTerm"
              placeholder="Search..."
              class="search-input filter-input"
              [style]="{
                'padding-left': '2rem',
                height: '32px',
                'border-radius': '4px',
              }"
            />
          </div>
        </div>

        <div class="filter-header-item date-item">
          <label for="eventStartDate">Event Start Date</label>
          <p-calendar
            id="eventStartDate"
            formControlName="eventStartDate"
            [showIcon]="true"
            [showButtonBar]="true"
            dateFormat="dd/mm/yy"
            placeholder="Select Date"
            styleClass="filter-input"
            [style]="{ height: '32px', width: '100%' }"
            [inputStyle]="{ height: '32px', 'font-size': '0.8rem' }"
            [showTime]="false"
            [showOnFocus]="false"
            [panelStyleClass]="''"
            [baseZIndex]="1000"
            (onSelect)="onDateSelect($event, 'eventStartDate')"
          ></p-calendar>
        </div>

        <div class="filter-header-item">
          <label for="eventStatus">Event Status</label>
          <p-dropdown
            id="eventStatus"
            formControlName="eventStatus"
            [options]="eventStatusOptions"
            optionLabel="name"
            optionValue="value"
            placeholder="All"
            styleClass="centered-dropdown filter-input"
            [style]="{ height: '32px', width: '100%' }"
            [panelStyleClass]="'text-center'"
          ></p-dropdown>
        </div>

        <div class="filter-header-item">
          <label for="organizer">Organizer</label>
          <p-dropdown
            id="organizer"
            formControlName="organizer"
            [options]="organizerOptions"
            optionLabel="name"
            optionValue="value"
            placeholder="All"
            styleClass="centered-dropdown filter-input"
            [style]="{ height: '32px', width: '100%' }"
            [panelStyleClass]="'text-center'"
          ></p-dropdown>
        </div>
      </div>

      <!-- Second Row -->
      <div class="filter-header-row">
        <div class="filter-header-item">
          <label for="approvalStatus">Approval Status</label>
          <p-dropdown
            id="approvalStatus"
            formControlName="approvalStatus"
            [options]="approvalStatusOptions"
            optionLabel="name"
            optionValue="value"
            placeholder="All"
            styleClass="centered-dropdown filter-input"
            [style]="{ height: '32px', width: '100%' }"
            [panelStyleClass]="'text-center'"
          ></p-dropdown>
        </div>

        <div class="filter-header-item">
          <label for="type">Type</label>
          <p-dropdown
            id="type"
            formControlName="type"
            [options]="typeOptions"
            optionLabel="name"
            optionValue="value"
            placeholder="All"
            styleClass="centered-dropdown filter-input"
            [style]="{ height: '32px', width: '100%' }"
            [panelStyleClass]="'text-center'"
          ></p-dropdown>
        </div>

        <div class="filter-header-item">
          <label for="category">Category</label>
          <p-dropdown
            id="category"
            formControlName="category"
            [options]="categoryOptions"
            optionLabel="name"
            optionValue="value"
            placeholder="All"
            styleClass="centered-dropdown filter-input"
            [style]="{ height: '32px', width: '100%' }"
            [panelStyleClass]="'text-center'"
          ></p-dropdown>
        </div>

        <div class="filter-header-item date-item">
          <label for="submittedOn">Submitted on</label>
          <p-calendar
            id="submittedOn"
            formControlName="submittedOn"
            [showIcon]="true"
            [showButtonBar]="true"
            dateFormat="dd/mm/yy"
            placeholder="Select Date"
            styleClass="filter-input"
            [style]="{ height: '32px', width: '100%' }"
            [inputStyle]="{ height: '32px', 'font-size': '0.8rem' }"
            [showTime]="false"
            [showOnFocus]="false"
            [panelStyleClass]="''"
            [baseZIndex]="1000"
            (onSelect)="onDateSelect($event, 'submittedOn')"
          ></p-calendar>
        </div>

        <div class="filter-header-item date-item">
          <label for="eventReviewedOn">Reviewed on</label>
          <p-calendar
            id="eventReviewedOn"
            formControlName="eventReviewedOn"
            [showIcon]="true"
            [showButtonBar]="true"
            dateFormat="dd/mm/yy"
            placeholder="Select Date"
            styleClass="filter-input"
            [style]="{ height: '32px', width: '100%' }"
            [inputStyle]="{ height: '32px', 'font-size': '0.8rem' }"
            [showTime]="false"
            [showOnFocus]="false"
            [panelStyleClass]="''"
            [baseZIndex]="1000"
            (onSelect)="onDateSelect($event, 'eventReviewedOn')"
          ></p-calendar>
        </div>
      </div>
    </form>
  </div>

  <!-- Loading Spinner -->
  <div
    *ngIf="isLoading"
    class="d-flex justify-content-center align-items-center my-5"
  >
    <p-progressSpinner
      strokeWidth="4"
      [style]="{ width: '50px', height: '50px' }"
    ></p-progressSpinner>
  </div>

  <!-- Error Message -->
  <div *ngIf="error && !isLoading" class="alert alert-danger">
    <div class="mb-2">{{ error }}</div>
    <button pButton class="p-button-sm p-button-danger" (click)="loadEvents()">
      <i class="pi pi-refresh me-1"></i> Retry
    </button>
  </div>

  <!-- No Events Message -->
  <div
    *ngIf="!isLoading && !error && filteredEvents.length === 0"
    class="alert alert-info"
  >
    <div class="mb-2">No events found.</div>
    <!-- <button pButton class="p-button-sm p-button-info" (click)="clearFilters()">
      <i class="pi pi-filter-slash me-1"></i> Clear Filters
    </button> -->
  </div>

  <!-- Event Cards -->
  <div *ngIf="!isLoading && !error && filteredEvents.length > 0">
    <div class="event-list">
      <div
        class="event-card-horizontal"
        *ngFor="let event of filteredEvents"
        (click)="viewEventDetails(event.id)"
      >
        <div class="event-card-content">
          <div class="event-image">
            <img
              [src]="
                event.eventImageUrl
                  ? getFullImagePath(event.eventImageUrl)
                  : 'assets/images/placeholder.jpg'
              "
              alt="Event Image"
              class="full-image"
            />
          </div>

          <div class="event-details">
            <div class="event-header">
              <div class="title-category">
                <h3 class="event-title">{{ event.title }}</h3>
                <div class="event-category">
                  <span class="category-text">{{
                    formatEventType(event.typeName || "")
                  }}</span>
                  <span class="category-separator">•</span>
                  <span class="category-text">{{
                    formatEventCategory(event.category)
                  }}</span>
                </div>
              </div>
              <span
                class="status-badge"
                [ngClass]="{
                  draft: event.statusName === 'Draft',
                  pending: event.statusName === 'Submitted',
                  approved:
                    event.statusName === 'Approved' && !hasEventStarted(event),
                  'event-started':
                    event.statusName === 'Approved' && hasEventStarted(event),
                  rejected: event.statusName === 'Rejected',
                  cancelled: event.statusName === 'Cancelled',
                  'pending-review': event.statusName === 'Submitted',
                }"
              >
                {{
                  event.statusName === "Draft"
                    ? "Draft"
                    : event.statusName === "Submitted"
                      ? "Pending Review"
                      : event.statusName === "Approved" &&
                          hasEventStarted(event)
                        ? "Event Has Started"
                        : event.statusName
                }}
              </span>
              <button
                pButton
                class="p-button-rounded p-button-text edit-button"
                [ngClass]="{ 'disabled-edit-button': hasEventStarted(event) }"
                (click)="
                  !hasEventStarted(event) && editEvent(event.id);
                  $event.stopPropagation()
                "
                *ngIf="
                  isGlobalAdmin ||
                  event.organizerId === authService.getUserInfo()?.id
                "
                [pTooltip]="
                  hasEventStarted(event)
                    ? 'Cannot edit event that has already started'
                    : ''
                "
                tooltipPosition="top"
              >
                <i class="pi pi-pencil"></i>
              </button>
            </div>

            <div class="event-info-container">
              <!-- Left Column -->
              <div class="event-info-column">
                <!-- Schedule Section -->
                <div class="event-info-row">
                  <div class="info-label">Schedule on</div>
                  <div class="info-value">
                    <i class="pi pi-calendar me-2"></i>
                    <span>
                      {{ event.eventStarts | date: "dd MMM yyyy" : "IST" }}
                      {{
                        event.displayStartTime && event.startTime
                          ? " - " + (event.startTime | slice: 0 : 5)
                          : ""
                      }}
                      To
                      {{ event.eventEnds | date: "dd MMM yyyy" : "IST" }}
                      {{
                        event.displayEndTime && event.endTime
                          ? " - " + (event.endTime | slice: 0 : 5)
                          : ""
                      }}
                    </span>
                  </div>
                </div>

                <!-- Submitted By Section -->
                <div class="event-info-row">
                  <div class="info-label">Submitted By</div>
                  <div class="info-value">
                    <i class="pi pi-user me-2"></i>
                    <span>{{
                      event.submitterName || event.organizerName
                    }}</span>
                  </div>
                </div>

                <!-- Reviewed By Section - Only for Approved or Rejected events -->
                <div
                  class="event-info-row reviewed-info"
                  *ngIf="
                    event.statusName === 'Approved' ||
                    event.statusName === 'Rejected'
                  "
                >
                  <div class="info-label">Reviewed By</div>
                  <div class="info-value">
                    <i class="pi pi-user me-2"></i>
                    <span>{{ event.reviewedByName || "Admin" }}</span>
                  </div>
                </div>
              </div>

              <!-- Right Column -->
              <div class="event-info-column">
                <!-- Location Section -->
                <div class="event-info-row">
                  <div class="info-label">Location</div>
                  <div class="info-value">
                    <!-- Online Event -->
                    <ng-container
                      *ngIf="event.locationType === EventLocationType.Online"
                    >
                      <i class="pi pi-globe me-2"></i>
                      <span>Online Event</span>
                    </ng-container>

                    <!-- Venue Event -->
                    <ng-container
                      *ngIf="
                        event.locationType === EventLocationType.Venue &&
                        event.location?.address1 &&
                        event.location.address1 !== 'N/A'
                      "
                    >
                      <i class="pi pi-map-marker me-2"></i>
                      <span>
                        {{ event.location.address1 }},
                        {{ event.location.city }}, {{ event.location.state }}
                        {{ event.location.zipCode }}
                      </span>
                    </ng-container>

                    <!-- No Location Info -->
                    <ng-container
                      *ngIf="
                        !event.locationType ||
                        (event.locationType === EventLocationType.Venue &&
                          (!event.location?.address1 ||
                            event.location.address1 === 'N/A'))
                      "
                    >
                      <i class="pi pi-exclamation-triangle me-2"></i>
                      <span>Location not specified</span>
                    </ng-container>
                  </div>
                </div>

                <!-- Submitted On Section -->
                <div class="event-info-row">
                  <div class="info-label">Submitted On</div>
                  <div class="info-value">
                    <i class="pi pi-calendar me-2"></i>
                    <span>{{
                      event.submittedOn || event.createdAt
                        | date: "dd MMM yyyy" : "IST"
                    }}</span>
                  </div>
                </div>

                <!-- Reviewed On Section - Only for Approved or Rejected events -->
                <div
                  class="event-info-row"
                  *ngIf="
                    event.statusName === 'Approved' ||
                    event.statusName === 'Rejected'
                  "
                >
                  <div class="info-label">Reviewed On</div>
                  <div class="info-value">
                    <i class="pi pi-calendar me-2"></i>
                    <span>{{
                      event.reviewedOn | date: "dd MMM yyyy" : "IST"
                    }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="d-flex justify-content-between align-items-center mt-3 mb-4">
    <!-- Entries info text -->
    <div class="text-muted">
      Showing {{ (currentPage - 1) * pageSize + 1 }} to
      {{ Math.min(currentPage * pageSize, totalItems) }} of
      {{ totalItems }} entries
    </div>

    <!-- Pagination -->
    <nav aria-label="Page navigation" *ngIf="totalPages > 1">
      <ul class="pagination mb-0">
        <li class="page-item" [class.disabled]="currentPage === 1">
          <a
            class="page-link"
            href="javascript:void(0)"
            (click)="onPageChange(currentPage - 1)"
            tabindex="-1"
          >
            Previous
          </a>
        </li>

        <li
          class="page-item"
          *ngFor="let page of pages"
          [class.active]="page === currentPage"
        >
          <a
            class="page-link"
            href="javascript:void(0)"
            (click)="onPageChange(page)"
          >
            {{ page }}
          </a>
        </li>

        <li class="page-item" [class.disabled]="currentPage === totalPages">
          <a
            class="page-link"
            href="javascript:void(0)"
            (click)="onPageChange(currentPage + 1)"
          >
            Next
          </a>
        </li>
      </ul>
    </nav>
  </div>
</div>
