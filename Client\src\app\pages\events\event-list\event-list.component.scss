// Global styles for PrimeNG components
:host ::ng-deep {
  // Style for dropdown placeholders
  .p-dropdown .p-dropdown-label.p-placeholder {
    text-align: center !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }
  .p-select-label {
    padding-top: 5px;
    padding-left: 8px;
  }

  // Style for centered text in dropdowns
  .text-center .p-dropdown-item {
    text-align: center;
  }

  // Style for centered dropdown class
  .centered-dropdown .p-dropdown-label {
    text-align: center !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  // Override for text inputs only
  .p-input-icon-left .p-inputtext,
  input.p-inputtext {
    text-align: left !important;
  }

  // // Keep dropdowns centered
  // p-dropdown .p-dropdown-label,
  // .p-dropdown .p-dropdown-label {
  //   text-align: center !important;
  //   display: flex !important;
  //   align-items: center !important;
  //   justify-content: center !important;
  // }
}

// Main container styles
.event-list-container {
  background-color: #f0f7ff;
  padding: 1.5rem;
  border-radius: 8px;
  max-width: 950px;
  margin: 0 auto;

  h2 {
    font-size: 1.75rem;
    font-weight: 600;
    color: #1e293b;
  }
}

// Button styles
.p-button-outlined.p-button-danger {
  border-color: #e11d48;
  color: #e11d48;

  &:hover {
    background-color: rgba(225, 29, 72, 0.04);
  }
}

.create-event-btn {
  background-color: #e11d48;
  border-color: #e11d48;

  &:hover {
    background-color: #be123c;
    border-color: #be123c;
  }
}

// Tab Styles
.event-tabs {
  max-width: 950px; // Match the container width
  margin: 0 auto;
}

.nav-tabs {
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 1.5rem;

  .nav-link {
    color: #64748b;
    border: none;
    padding: 0.75rem 1rem;
    font-weight: 500;
    font-size: 0.95rem;
    margin-right: 0.5rem;

    &.active {
      color: #e11d48;
      border-bottom: 2px solid #e11d48;
      background-color: transparent;
      font-weight: 600;
    }

    &:hover:not(.active) {
      color: #334155;
      border-bottom: 2px solid #e5e7eb;
    }

    .badge {
      background-color: #e11d48;
      color: white;
      font-size: 0.75rem;
      padding: 0.2rem 0.5rem;
      border-radius: 9999px;
      margin-left: 0.5rem;
    }
  }
}

.filter-btn {
  border-radius: 8px;
}

// Filter section styles
.filter-section {
  background-color: white;
  border-radius: 8px;
  padding: 1.25rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;

  .filter-header {
    h5 {
      font-weight: 600;
      color: #334155;
    }
  }
}

// Compact filter section styles
.compact-filter-section {
  background-color: white;
  border-radius: 8px;
  padding: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
  max-width: 950px;
  margin-left: auto;
  margin-right: auto;

  // Filter header row
  .filter-header-row {
    display: flex;
    flex-wrap: nowrap;
    gap: 8px;
    margin-bottom: 10px;

    .filter-header-item {
      display: flex;
      flex-direction: column;
      flex: 1;

      label {
        display: block;
        font-size: 0.7rem;
        color: #64748b;
        margin-bottom: 0.15rem;
        font-weight: 500;
      }

      .filter-input {
        min-width: 120px;
        height: 32px;
      }

      &.date-item {
        .filter-input {
          width: 150px;
        }

        .p-calendar {
          width: 100%;
        }
      }
    }

    @media (max-width: 992px) {
      flex-wrap: wrap;

      .filter-header-item {
        flex: 0 0 calc(50% - 4px);
      }
    }

    @media (max-width: 576px) {
      .filter-header-item {
        flex: 0 0 100%;
      }
    }
  }

  .filter-header {
    margin-bottom: 0.5rem;
    h5 {
      font-weight: 600;
      color: #334155;
      font-size: 0.9rem;
    }
  }

  .filter-row {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.5rem;

    &:last-child {
      margin-bottom: 0;
    }

    @media (max-width: 992px) {
      flex-wrap: wrap;
    }
  }

  .filter-group {
    flex: 1;
    min-width: 0;

    @media (max-width: 992px) {
      flex: 0 0 calc(50% - 0.25rem);
    }

    @media (max-width: 576px) {
      flex: 0 0 100%;
    }

    label {
      display: block;
      font-size: 0.7rem;
      color: #64748b;
      margin-bottom: 0.15rem;
      font-weight: 500;
    }

    .p-dropdown,
    .p-calendar,
    input {
      font-size: 0.8rem;
    }

    &.date-group {
      display: flex;
      gap: 0.5rem;
      flex: 1.5; // Give date group more space

      .date-filter {
        flex: 1;
        min-width: 120px; // Ensure minimum width for date fields
        max-width: 160px; // Limit maximum width
        margin-right: 5px; // Add some spacing between date fields
      }

      @media (max-width: 1200px) {
        flex-direction: column;
        gap: 0.5rem;
      }
    }
  }

  :host ::ng-deep {
    .p-dropdown {
      .p-dropdown-label {
        padding: 0.25rem 0.5rem;
      }

      .p-dropdown-label.p-placeholder {
        text-align: center !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
      }

      .p-dropdown-trigger {
        width: 1.75rem;
      }
    }

    .p-calendar {
      .p-inputtext {
        padding: 0.25rem 0.5rem;
        height: 32px;
        text-align: center;
        font-size: 0.75rem !important;
        width: 100px !important;

        &::placeholder {
          text-align: center;
          visibility: visible !important;
          opacity: 1 !important;
          color: #64748b !important;
        }
      }
      .p-datepicker-trigger {
        width: 1.75rem;
        height: 32px;
      }
    }

    // Special styling for filter header row calendar
    .filter-header-row {
      .filter-header-item.date-item {
        .p-calendar {
          .p-inputtext {
            width: 120px !important;
          }
        }
      }
    }

    .p-inputtext {
      padding: 0.25rem 0.5rem;
      height: 32px;
    }

    .p-button.p-button-icon-only {
      width: 1.75rem;
      height: 1.75rem;
    }

    .p-input-icon-left > .p-inputtext {
      padding-left: 2rem;
    }

    .p-input-icon-left > i:first-of-type {
      left: 0.75rem;
      color: #64748b;
    }

    .search-input {
      height: 32px;
      border-radius: 4px;
    }
  }
}

// Fix for search input styling
.p-input-icon-left {
  width: 100%;
  position: relative;

  i {
    position: absolute;
    left: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    color: #64748b;
    z-index: 1;
    font-size: 0.8rem;
  }

  .search-input {
    width: 100%;
    height: 32px;
    border-radius: 4px;
    padding-left: 2rem !important;
    background-color: white;
    border: 1px solid #ced4da;
    font-size: 0.8rem;
    text-align: left !important; // Force left alignment for text input

    &::placeholder {
      text-align: left;
    }
  }
}

// Override any global text-center styles for inputs
:host ::ng-deep {
  .p-inputtext {
    text-align: left !important;
  }
}

// Events Container with Scrollable Area
.events-container {
  max-width: 950px;
  margin: 0 auto;
  height: 500px; // Fixed height for the scrollable area
  overflow: hidden;

  // Performance optimizations for container
  contain: layout style paint; // CSS containment for better performance
  transform: translateZ(0); // Force hardware acceleration
}

.event-list-scrollable {
  height: 100%;
  overflow-y: auto;
  padding-right: 8px; // Space for scrollbar

  // Performance optimizations for smooth scrolling
  -webkit-overflow-scrolling: touch; // iOS smooth scrolling
  scroll-behavior: smooth; // Smooth scrolling behavior
  will-change: scroll-position; // Optimize for scrolling
  transform: translateZ(0); // Force hardware acceleration

  // Custom scrollbar styling
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }

  // For Firefox
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

// Horizontal Event Card Styles
.event-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem; // Reduced gap between cards
  max-width: 950px; // Match the Figma design width
  margin: 0 auto;
  margin-bottom: 0;
}

.event-card-horizontal {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  cursor: pointer;
  width: 100%;
  margin-bottom: 9px;
  min-height: auto; // Remove fixed min-height
  display: flex;

  // Performance optimizations
  will-change: transform; // Optimize for transform changes
  transform: translateZ(0); // Force hardware acceleration
  backface-visibility: hidden; // Prevent flickering

  // Optimized transitions - only on hover, not during scroll
  transition: none; // Remove default transitions

  &:hover {
    transition:
      transform 0.15s ease-out,
      box-shadow 0.15s ease-out; // Faster, smoother transitions
    transform: translateY(-1px) translateZ(0); // Reduced movement, maintain hardware acceleration
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12); // Lighter shadow for better performance
  }

  .event-card-content {
    display: flex;
    width: 100%;
    height: 100%;

    .event-image {
      padding: 10px;
      width: 210px;
      min-width: 144px;
      height: 218px;
      overflow: hidden;
      margin: 0;
      border-radius: 0; // Remove border radius to match Figma

      img {
        width: 100%;
        height: 100%;
        border-radius: 8px; // Remove border radius to match Figma
        object-fit: cover;

        // Performance optimizations for images
        transform: translateZ(0); // Force hardware acceleration
        backface-visibility: hidden; // Prevent flickering
        image-rendering: -webkit-optimize-contrast; // Optimize image rendering
        image-rendering: crisp-edges; // Better performance on some browsers

        &.full-image {
          object-position: center;
          display: block;
        }
      }
    }

    .event-details {
      flex: 1;
      padding: 0.75rem 1rem; // Adjusted padding to match Figma

      .event-header {
        display: flex;
        align-items: flex-start;
        margin-bottom: 0.5rem; // Reduced margin
        position: relative;

        .title-category {
          flex: 1;
          margin-right: 1rem;

          .event-title {
            font-size: 18px; // Smaller font size
            font-weight: 600;
            color: #1e293b;
            margin: 0 0 0.25rem 0;
            padding-right: 5rem;
          }

          .event-category {
            display: flex;
            align-items: center;

            .category-text {
              font-size: 0.8rem; // Smaller font size
              color: #0284c7; // Blue color for categories
            }

            .category-separator {
              color: #94a3b8; // Light gray color for separator
              font-size: 0.8rem;
              margin: 0 0.25rem;
            }
          }
        }

        .status-badge {
          padding: 0.15rem 0.5rem; // Smaller padding
          border-radius: 9999px;
          font-size: 0.7rem; // Smaller font size
          font-weight: 500;
          margin-right: 0.5rem;
          white-space: nowrap;
          height: fit-content;
          position: absolute;
          right: 2.5rem;
          top: 0.1rem; // Adjust vertical position slightly
        }
      }

      .event-info-container {
        display: flex;
        flex-wrap: wrap;
        margin-top: 0.5rem;
        gap: 0.5rem; // Reduced gap to match Figma
      }

      .event-info-column {
        flex: 1;
        min-width: 45%; // Ensure columns take reasonable width
      }

      .event-info-row {
        margin-bottom: 0.5rem; // Reduced margin to match Figma

        .info-label {
          color: #64748b;
          font-size: 0.7rem;
          margin-bottom: 0.15rem; // Reduced margin to match Figma
          font-weight: 500;
        }

        .info-value {
          display: flex;
          align-items: center;
          font-size: 0.8rem;
          color: #334155;
          white-space: normal;
          word-break: break-word;
          line-height: 1.3; // Adjusted line height to match Figma

          i {
            color: #64748b;
            font-size: 0.8rem;
            min-width: 16px;
            margin-right: 0.5rem; // Ensure consistent spacing after icons

            &.pi-globe {
              color: #0d6efd; // Blue for online events
            }

            &.pi-map-marker {
              color: #dc3545; // Red for venue events
            }

            &.pi-exclamation-triangle {
              color: #ffc107; // Yellow for missing location
            }
          }
        }
      }
    }
  }
}

.status-badge {
  padding: 0.15rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.7rem;
  font-weight: 500;
  margin-right: 0.5rem;
  white-space: nowrap;
  height: fit-content;

  &.draft {
    background-color: #f5f5f5; // Light gray background
    color: #757575; // Dark gray text
  }

  &.approved {
    background-color: #e8f5e9; // Light green background (matches Figma)
    color: #2e7d32; // Dark green text (matches Figma)
  }

  &.event-started {
    background-color: #e3f2fd; // Light blue background
    color: #1565c0; // Dark blue text
  }

  &.rejected {
    background-color: #ffebee; // Light red background (matches Figma)
    color: #c62828; // Dark red text (matches Figma)
  }

  &.pending {
    background-color: #fff8e1; // Light yellow background (matches Figma)
    color: #f57f17; // Dark yellow/amber text (matches Figma)
  }

  &.pending-review {
    background-color: #fff8e1; // Light yellow background (matches Figma)
    color: #f57f17; // Dark yellow/amber text (matches Figma)
  }

  &.cancelled {
    background-color: #eceff1; // Light gray background
    color: #546e7a; // Dark gray text
  }

  &.draft {
    background-color: #e3f2fd; // Light blue background
    color: #1565c0; // Dark blue text
  }
}

// Event header with status badge positioning
.event-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.5rem;
  position: relative;

  .title-category {
    flex: 1;
    margin-right: 1rem;

    .event-title {
      font-size: 1.1rem;
      font-weight: 600;
      color: #1e293b;
      margin: 0 0 0.25rem 0;
      padding-right: 5rem;
    }

    .event-category {
      display: flex;
      align-items: center;
      gap: 0.25rem; // Reduced gap between category items to match Figma

      .category-text {
        font-size: 0.8rem;
        color: #0284c7; // Blue color for categories
      }

      .category-separator {
        color: #94a3b8; // Light gray color for separator
        font-size: 0.8rem;
      }
    }
  }

  .status-badge {
    position: absolute;
    right: 2.5rem;
    top: 0.1rem; // Adjust vertical position slightly
  }

  // Edit button styling - updated to match Figma design
  .edit-button {
    position: absolute;
    right: 0;
    top: 0;
    width: 2rem;
    height: 2rem;
    color: #ff9f43; // Changed to match Figma design (orange color)
    background: transparent;
    border: none;

    &:hover {
      background-color: rgba(255, 159, 67, 0.1); // Updated hover color
    }

    i {
      font-size: 1rem;
    }

    &.disabled-edit-button {
      opacity: 0.5;
      cursor: not-allowed;

      &:hover {
        background-color: transparent;
      }
    }
  }
}

// Pagination Styles - Using global styles from styles.scss

.app-container {
  overflow: hidden;
  min-height: auto;
}
